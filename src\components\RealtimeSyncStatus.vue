<template>
  <div class="sync-status" :class="statusClass">
    <div class="status-indicator">
      <span class="status-dot" :class="dotClass"></span>
      <span class="status-text">{{ statusText }}</span>
    </div>
    <div class="connection-info" v-if="showDetails">
      <div class="info-item">
        <span class="label">连接模式:</span>
        <span class="value">{{ connectionMode }}</span>
      </div>
      <div class="info-item" v-if="lastUpdate">
        <span class="label">最后更新:</span>
        <span class="value">{{ formatTime(lastUpdate) }}</span>
      </div>
      <div class="info-item" v-if="isUsingFallback">
        <span class="label">回退模式:</span>
        <span class="value">轮询中</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue';
import { useSocket } from '../services/socketService';

// 获取WebSocket状态
const { connected, lastUpdate, connectionMode, isUsingFallback } = useSocket();

// 控制详细信息显示
const showDetails = ref(false);

// 计算状态类名
const statusClass = computed(() => {
  if (connected.value) {
    return 'status-connected';
  } else {
    return 'status-disconnected';
  }
});

// 计算状态点类名
const dotClass = computed(() => {
  if (connected.value) {
    return 'dot-connected';
  } else {
    return 'dot-disconnected';
  }
});

// 计算状态文本
const statusText = computed(() => {
  if (connected.value) {
    if (isUsingFallback.value) {
      return '已连接 (轮询模式)';
    } else {
      return '实时同步';
    }
  } else {
    return '连接断开';
  }
});

// 格式化时间
function formatTime(date) {
  if (!date) return '';
  return new Date(date).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
}

// 切换详细信息显示
function toggleDetails() {
  showDetails.value = !showDetails.value;
}
</script>

<style scoped>
.sync-status {
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
  background: #f9f9f9;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.sync-status:hover {
  background: #f0f0f0;
}

.status-connected {
  border-color: #4caf50;
  background: #e8f5e8;
}

.status-disconnected {
  border-color: #f44336;
  background: #ffeaea;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
}

.dot-connected {
  background: #4caf50;
  animation: pulse 2s infinite;
}

.dot-disconnected {
  background: #f44336;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.status-text {
  font-weight: 500;
  color: #333;
}

.connection-info {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #e0e0e0;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.label {
  color: #666;
}

.value {
  color: #333;
  font-weight: 500;
}
</style>
