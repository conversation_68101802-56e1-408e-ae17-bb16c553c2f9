<template>
  <div>

    <!-- 添加新记录表单 -->
    <form @submit.prevent="handleAddRecord" class="add-finance-form">
      <div class="form-header">
        <h3>{{ isEditing ? '编辑销售记录' : '添加销售记录' }}</h3>
        <div class="form-actions" v-if="isEditing">
          <button type="button" class="cancel-edit-btn" @click="cancelEditing">取消编辑</button>
        </div>
      </div>
      <input type="date" v-model="newRecord.date" required />

      <!-- 主要输入行：商品、售货员、金额 -->
      <div class="main-input-row">
        <!-- 商品选择组合框 -->
        <div class="product-combobox">
          <input
            ref="productInputRef"
            type="text"
            v-model="productInput"
            :placeholder="isProductSearchMode ? '输入商品名称搜索' : '点击选择商品'"
            :readonly="!isProductSearchMode"
            @click="handleProductInputClick"
            @blur="handleProductBlur"
            @input="handleProductInput"
            @keydown.enter.prevent="handleProductEnter"
            @keydown.down.prevent="highlightNextProduct"
            @keydown.up.prevent="highlightPrevProduct"
            @keydown.escape="exitSearchMode"
            aria-haspopup="listbox"
            :aria-expanded="isProductDropdownOpen"
            required
          />
          <ul
            v-if="isProductDropdownOpen && (filteredProducts.length > 0 || !isProductSearchMode)"
            class="product-dropdown"
            role="listbox"
          >
            <!-- 手动输入选项（仅在非搜索模式下显示） -->
            <li
              v-if="!isProductSearchMode"
              :class="{ highlighted: highlightedProductIndex === -1 }"
              @mousedown.prevent="enterSearchMode"
              class="search-option"
              role="option"
            >
              <div class="search-option-content">
                <span class="search-icon">🔍</span>
                <span class="search-text">手动输入搜索</span>
              </div>
            </li>
            <!-- 商品列表 -->
            <li
              v-for="(product, index) in filteredProducts"
              :key="product.id"
              :class="{ highlighted: index === (isProductSearchMode ? highlightedProductIndex : highlightedProductIndex - 1) }"
              @mousedown.prevent="selectProduct(product)"
              role="option"
              :aria-selected="index === (isProductSearchMode ? highlightedProductIndex : highlightedProductIndex - 1)"
            >
              <div class="product-option">
                <span class="product-name">{{ product.name }}</span>
                <span class="product-price">¥{{ product.price.toFixed(2) }}</span>
                <span class="product-stock" :class="{ 'low-stock': product.remaining <= 5, 'out-of-stock': product.remaining === 0 }">
                  库存: {{ product.remaining }}
                </span>
              </div>
            </li>
          </ul>
        </div>

        <!-- 新的售货员输入/选择区域 -->
        <div class="salesperson-combobox">
          <input
            type="text"
            v-model="salespersonInput"
            placeholder="查找或添加售货员"
            @focus="isSalespersonDropdownOpen = true"
            @blur="handleSalespersonBlur"
            @input="handleSalespersonInput"
            @keydown.enter.prevent="handleSalespersonEnter"
            @keydown.down.prevent="highlightNextSalesperson"
            @keydown.up.prevent="highlightPrevSalesperson"
            aria-haspopup="listbox"
            :aria-expanded="isSalespersonDropdownOpen"
          />
          <ul
            v-if="isSalespersonDropdownOpen && filteredSalespersonsWithAction.length > 0"
            class="salesperson-dropdown"
            role="listbox"
          >
            <li
              v-for="(item, index) in filteredSalespersonsWithAction"
              :key="item.value + item.type"
              :class="{ highlighted: index === highlightedSalespersonIndex, 'add-option': item.type === 'add' }"
              @mousedown.prevent="selectSalesperson(item)"
              role="option"
              :aria-selected="index === highlightedSalespersonIndex"
            >
              <!-- 如果是现有售货员，显示为 Tag -->
              <el-tag
                v-if="item.type === 'select'"
                :type="getTagType(item.value)"
                effect="light"
                round
                closable
                @close.stop="handleDeleteSalesperson(item.value)"
              >
                {{ item.text }}
              </el-tag>
              <!-- 如果是添加选项，显示为普通文本 -->
              <span v-else-if="item.type === 'add'">
                {{ item.text }}
              </span>
            </li>
          </ul>
        </div>

        <!-- 售出金额输入 -->
        <input type="number" step="0.01" v-model.number="newRecord.saleAmount" placeholder="售出金额" required />
      </div>

      <div class="time-input" v-if="isEditing">
        <label for="edit-time">时间:</label>
        <input type="time" id="edit-time" v-model="newRecord.time" required>
      </div>

      <textarea v-model="newRecord.notes" placeholder="备注" rows="2" class="notes-input"></textarea>
      <button type="submit">{{ isEditing ? '保存修改' : '添加记录' }}</button>
      <p v-if="addRecordError" class="error-message">{{ addRecordError }}</p>
    </form>

    <hr />

    <!-- 财务记录表格 -->
    <h2>今日账单 ({{ today }})</h2>
    <table class="finance-table" id="finance-table">
      <thead>
        <tr>
          <th>日期 / 时间</th>
          <th>卖出物品</th>
          <th>售货员</th>
          <th>售出金额</th>
          <th class="rebate-header">
            返利({{ (rebateRate * 100).toFixed(0) }}%)
            <button @click="showRebateEditor = true" class="edit-rebate-btn" title="修改返利系数">
              <span class="edit-icon">✎</span>
            </button>
          </th>
          <th>利润</th>
          <th>备注</th> <!-- 新增备注列 -->
          <th>操作</th>
        </tr>
      </thead>
      <tbody>
        <tr v-if="financialRecords.length === 0">
          <td colspan="7" style="text-align: center;">暂无记录</td>
        </tr>
        <tr v-for="record in sortedRecords" :key="record.id">
          <td>{{ record.date }} {{ record.time || '' }}</td>
          <td>{{ getProductName(record.productId) }}</td>
          <td>
            <!-- 使用 el-tag 组件 -->
            <el-tag
              v-if="record.salesperson"
              :type="getTagType(record.salesperson)"
              effect="light"
              round>
              {{ record.salesperson }}
            </el-tag>
            <span v-else>-</span>
          </td>
          <td>¥{{ record.saleAmount.toFixed(2) }}</td>
          <td>
            <template v-if="record.salesperson">
              ¥{{ calculateRebate(record).toFixed(2) }}
            </template>
            <template v-else>-</template>
          </td>
          <td>¥{{ calculateProfit(record).toFixed(2) }}</td>
          <td class="notes-cell" :title="record.notes">{{ record.notes || '-' }}</td> <!-- 显示备注 -->
          <td>
             <button @click="openEditModal(record)" class="edit-button">编辑</button>
             <button @click="deleteRecord(record.id)" class="delete-button">删除</button>
          </td>
        </tr>
      </tbody>
      <tfoot>
        <tr>
          <td colspan="3"><strong>今日总计</strong></td>
          <td><strong>¥{{ todayTotalSaleAmount.toFixed(2) }}</strong></td>
          <td><strong>¥{{ todayTotalRebate.toFixed(2) }}</strong></td>
          <td><strong>¥{{ todayTotalProfit.toFixed(2) }}</strong></td>
          <td colspan="2"></td> <!-- 总计行不需要操作 -->
        </tr>
      </tfoot>
    </table>

    <!-- 新增：当日结账总结显示区域 -->
    <div v-if="showDailySummary" class="daily-summary" id="daily-summary">
      <h3>当日结账总结 ({{ dailySummary.date }})</h3>
      <div class="summary-content">
        <div class="summary-main">
          <p><strong>总流水:</strong> ¥{{ dailySummary.totalRevenue.toFixed(2) }}</p>
          <p><strong>总利润:</strong> ¥{{ dailySummary.totalProfit.toFixed(2) }}</p>
          <p><strong>销售冠军:</strong> {{ dailySummary.topSalesperson || '无' }} (销售额: ¥{{ dailySummary.topSalespersonAmount.toFixed(2) }})</p>
        </div>
        <div class="summary-details">
          <div class="summary-section">
            <h4>商品销售情况:</h4>
            <ul>
              <li v-for="(item, name) in dailySummary.productSales" :key="name">
                {{ name }}: 售出 {{ item.quantity }} 件, 金额 ¥{{ item.amount.toFixed(2) }}
              </li>
            </ul>
          </div>
          <div class="summary-section">
            <h4>销售员返利:</h4>
            <ul>
              <li v-for="(rebate, name) in dailySummary.salespersonRebates" :key="name">
                {{ name }}: ¥{{ rebate.toFixed(2) }}
              </li>
            </ul>
          </div>
        </div>
      </div>
      <button @click="showDailySummary = false" class="close-summary-btn">关闭总结</button>
    </div>

    <!-- 编辑弹窗 -->
    <edit-finance-record-modal
      v-if="editingRecord"
      :record="editingRecord"
      @close="closeEditModal"
      @save="handleSaveEdit"
    />

    <!-- 反馈消息 -->
    <div v-if="feedbackMessage" class="feedback" :class="feedbackType">
      {{ feedbackMessage }}
    </div>

    <!-- 保存状态指示器 -->
    <div v-if="saveStatus !== 'idle'" class="save-status" :class="saveStatus">
      <span v-if="saveStatus === 'saving'" class="save-indicator">
        💾 正在保存数据...
      </span>
      <span v-else-if="saveStatus === 'success'" class="save-indicator">
        ✅ 数据已保存
        <span v-if="lastSaveTime" class="save-time">
          ({{ formatSaveTime(lastSaveTime) }})
        </span>
      </span>
      <span v-else-if="saveStatus === 'error'" class="save-indicator">
        ⚠️ 保存失败，数据已保存到本地
      </span>
    </div>

    <!-- 返利系数编辑器弹窗 -->
    <div v-if="showRebateEditor" class="rebate-editor-modal">
      <div class="rebate-editor-content">
        <h3>修改返利系数</h3>
        <div class="rebate-editor-form">
          <div class="rate-input-group modal-input-group">
            <input
              type="number"
              v-model="rebateRatePercent"
              min="1"
              max="100"
              step="1"
              ref="rebateInput"
            />
            <span class="percent-sign">%</span>
          </div>
          <div class="rebate-editor-buttons">
            <button @click="updateRebateRateSetting" class="save-rebate-btn">保存</button>
            <button @click="showRebateEditor = false" class="cancel-rebate-btn">取消</button>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted } from 'vue';
import html2canvas from 'html2canvas'; // 导入 html2canvas
import * as XLSX from 'xlsx'; // 导入 xlsx
import { useFinance } from '../services/financeService';
import { useProducts } from '../services/productService'; // 需要商品数据
import { useSettings } from '../services/settingsService'; // 导入设置服务
import EditFinanceRecordModal from '../components/EditFinanceRecordModal.vue'; // <-- 导入编辑弹窗组件

const { products, getProductName } = useProducts(); // <-- 合并调用，同时获取 products 和 getProductName
const {
    financialRecords,
    salespersons,
    addFinancialRecord,
    addSalesperson,
    deleteSalesperson,
    totalSaleAmount,
    totalProfit,
    updateFinancialRecord,
    deleteFinancialRecord,
    // 保存状态
    isSaving,
    lastSaveTime,
    saveStatus
} = useFinance(); // <-- 添加 updateFinancialRecord 和 deleteFinancialRecord 以及保存状态
const { getRebateRate, updateRebateRate } = useSettings(); // 获取设置服务

// 获取 WebSocket 连接状态
import { useSocket } from '../services/socketService';
const { connected: socketConnected, lastUpdate: socketLastUpdate } = useSocket();

// 返利比例设置
const rebateRate = computed(() => getRebateRate(today.value)); // 获取当天的返利比例
const rebateRatePercent = ref(50); // 初始值，将在onMounted中更新
const showRebateEditor = ref(false); // 控制返利系数编辑器弹窗的显示

// --- 新增：结账相关状态 ---
const showDailySummary = ref(false);
const dailySummary = ref({
  date: '',
  totalRevenue: 0,
  totalProfit: 0,
  topSalesperson: '',
  topSalespersonAmount: 0,
  productSales: {},
  salespersonRebates: {}
});
// ------------------------

// 使用函数获取当前日期和时间，确保每次使用时都是最新的，并且使用北京时间（东八区）
function getCurrentDate() {
  // 获取当前日期（使用北京时间/东八区）
  const now = new Date();
  // 获取年、月、日（北京时间）
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要+1
  const day = String(now.getDate()).padStart(2, '0');
  // 返回格式化的日期字符串 YYYY-MM-DD
  return `${year}-${month}-${day}`;
}

function getCurrentTime() {
  // 获取当前时间（使用北京时间/东八区）
  const now = new Date();
  // 获取小时、分钟（北京时间）
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  // 返回格式化的时间字符串 HH:MM
  return `${hours}:${minutes}`;
}

const newRecord = ref({
  date: getCurrentDate(),
  productId: '',
  salesperson: null,
  saleAmount: null,
  notes: '',
  time: getCurrentTime(), // 默认当前时间
});

const addRecordError = ref('');
const isEditing = ref(false); // 表单是否处于编辑模式
const editingId = ref(null); // 当前正在编辑的记录ID

// --- 新增：编辑弹窗状态 ---
const editingRecord = ref(null);
// --- 结束：编辑弹窗状态 ---

// --- 新增：售货员组合框状态 ---
const salespersonInput = ref(''); // 输入框的当前值
const isSalespersonDropdownOpen = ref(false);
const highlightedSalespersonIndex = ref(-1); // 用于键盘导航
let blurTimeout = null; // 用于处理 blur 和 click 的竞态条件
// --- 结束：售货员组合框状态 ---

// --- 新增：商品组合框状态 ---
const productInput = ref(''); // 商品输入框的当前值
const isProductDropdownOpen = ref(false);
const highlightedProductIndex = ref(-1); // 用于键盘导航
const isProductSearchMode = ref(false); // 是否处于搜索模式
const productInputRef = ref(null); // 输入框引用
let productBlurTimeout = null; // 用于处理 blur 和 click 的竞态条件
// --- 结束：商品组合框状态 ---

// --- 计算属性 --- //
// 获取当前在售的商品列表（库存大于0或所有商品，根据需要调整）
const availableProducts = computed(() => {
  // return products.value.filter(p => p.remaining > 0);
  return products.value; // 或者显示所有商品
});

// 获取当天日期（使用北京时间/东八区）
const today = computed(() => getCurrentDate()); // YYYY-MM-DD 格式

// 只显示当天的记录，并按时间降序排序
const sortedRecords = computed(() => {
    // 筛选当天的记录
    const todayRecords = financialRecords.value.filter(record => record.date === today.value);
    // 按时间降序排序（最新的在上面）
    return [...todayRecords].sort((a, b) => {
        // 如果都有时间字段，按时间降序排序
        if (a.time && b.time) {
            return b.time.localeCompare(a.time);
        }
        // 如果只有一个有时间，有时间的排在前面（更新）
        if (a.time && !b.time) {
            return -1;
        }
        if (!a.time && b.time) {
            return 1;
        }
        // 如果都没有时间，按ID降序排序（假设ID是按时间顺序生成的）
        return b.id - a.id;
    });
});

// 今日总销售额
const todayTotalSaleAmount = computed(() => {
    return sortedRecords.value.reduce((sum, record) => sum + (record.saleAmount || 0), 0);
});

// 今日总返利
const todayTotalRebate = computed(() => {
    return sortedRecords.value.reduce((sum, record) => sum + calculateRebate(record), 0);
});

// 今日总利润
const todayTotalProfit = computed(() => {
    return sortedRecords.value.reduce((sum, record) => sum + calculateProfit(record), 0);
});

// --- 新增：过滤售货员列表 ---
const filteredSalespersons = computed(() => {
    const inputLower = salespersonInput.value.toLowerCase();
    if (!inputLower) {
        // 如果输入为空，显示所有（除了'无'，如果 financeService 中还返回的话）
        return salespersons.value.filter(p => p !== '无');
    }
    return salespersons.value.filter(p => p !== '无' && p.toLowerCase().includes(inputLower));
});

const filteredSalespersonsWithAction = computed(() => {
    const options = filteredSalespersons.value.map(p => ({ text: p, value: p, type: 'select' }));
    const inputTrimmed = salespersonInput.value.trim();
    const exactMatch = salespersons.value.some(p => p === inputTrimmed);

    // 如果输入了内容，且不在现有列表中，则添加"创建"选项
    if (inputTrimmed && !exactMatch) {
        options.push({ text: `添加 "${inputTrimmed}"`, value: inputTrimmed, type: 'add' });
    }
    return options;
});
// --- 结束：过滤售货员列表 ---

// --- 新增：过滤商品列表 ---
const filteredProducts = computed(() => {
    // 如果不在搜索模式，显示所有商品
    if (!isProductSearchMode.value) {
        return products.value;
    }

    // 在搜索模式下，根据输入进行过滤
    const inputLower = productInput.value.toLowerCase();
    if (!inputLower) {
        return products.value;
    }
    return products.value.filter(product =>
        product.name.toLowerCase().includes(inputLower)
    );
});
// --- 结束：过滤商品列表 ---

// --- 方法 --- //
// 使用从 productService 导入的 getProductName 函数

// --- 修改：根据姓名生成固定的 Tag 类型 ---
const tagTypes = ['success', 'info', 'warning']; // 其他售货员的备选类型
const salespersonTagTypeCache = {};
let otherTypeIndex = 0; // 用于其他售货员的索引

function getTagType(name) {
    if (!name) return '';

    // 固定颜色
    if (name === '李洁涵') {
        return 'danger'; // 红色
    }
    if (name === '刘昕怡') {
        return ''; // 蓝色 (默认主题色)
        // 或者明确指定 primary: return 'primary';
    }

    // 对于其他售货员，从备选类型中轮流选择
    if (salespersonTagTypeCache[name]) {
        return salespersonTagTypeCache[name];
    }
    const type = tagTypes[otherTypeIndex % tagTypes.length];
    salespersonTagTypeCache[name] = type;
    otherTypeIndex++;
    return type;
}
// --- 结束：修改 Tag 类型逻辑 ---

// 计算单条记录的返利
function calculateRebate(record) {
  return record.salesperson ? Math.round((record.saleAmount || 0) * rebateRate.value) : 0;
}

// 计算单条记录的利润
function calculateProfit(record) {
  const saleAmount = record.saleAmount || 0;
  const rebate = calculateRebate(record);
  return saleAmount - rebate;
}

// 注意：handleProductSelect 函数已移除，因为我们现在使用商品组合框

// --- 修改/新增：售货员组合框方法 ---
function handleSalespersonInput() {
    isSalespersonDropdownOpen.value = true;
    highlightedSalespersonIndex.value = -1; // Reset highlight on input
    // 将当前选中的 salesperson 与输入同步（如果用户正在编辑）
    if (newRecord.value.salesperson && newRecord.value.salesperson !== salespersonInput.value) {
        newRecord.value.salesperson = null;
    }
}

function handleSalespersonBlur() {
    // 延迟关闭，以便可以点击下拉项
    blurTimeout = setTimeout(() => {
        isSalespersonDropdownOpen.value = false;
        highlightedSalespersonIndex.value = -1;
        // 如果输入框内容不是一个有效的售货员，清空它或重置
        if (salespersonInput.value && !salespersons.value.includes(salespersonInput.value)) {
           // 可选：清空无效输入
           // salespersonInput.value = '';
           // newRecord.value.salesperson = null;
        }
         // 如果输入框有值但未确认选择，则尝试匹配或清空
        if (salespersonInput.value && newRecord.value.salesperson !== salespersonInput.value) {
             const exactMatch = salespersons.value.find(p => p === salespersonInput.value);
             if (exactMatch) {
                 newRecord.value.salesperson = exactMatch;
             } else {
                 // 清空输入和选择，因为用户没有确认添加
                 salespersonInput.value = '';
                 newRecord.value.salesperson = null;
             }
        } else if (!salespersonInput.value) {
             newRecord.value.salesperson = null; // 如果清空了输入，也清空选择
        }
    }, 200); // 200ms 延迟
}

function selectSalesperson(item) {
    clearTimeout(blurTimeout); // 取消关闭
    if (item.type === 'select') {
        salespersonInput.value = item.value;
        newRecord.value.salesperson = item.value;
        isSalespersonDropdownOpen.value = false;
        highlightedSalespersonIndex.value = -1;
    } else if (item.type === 'add') {
        addNewSalespersonAction(item.value);
    }
}

function addNewSalespersonAction(name) {
    const trimmedName = name.trim();
    if (trimmedName) {
        const result = addSalesperson(trimmedName);
        if (result.success) {
            salespersonInput.value = trimmedName;
            newRecord.value.salesperson = trimmedName;
            isSalespersonDropdownOpen.value = false;
            highlightedSalespersonIndex.value = -1;
        } else {
            alert(result.message);
            // 保留输入框内容让用户修改
            isSalespersonDropdownOpen.value = true; // 保持下拉打开以便看到错误或重试
        }
    } else {
        isSalespersonDropdownOpen.value = false; // 输入为空，关闭下拉
    }
}

function handleSalespersonEnter() {
    if (isSalespersonDropdownOpen.value && highlightedSalespersonIndex.value >= 0) {
        // 如果有高亮项，选择它
        const selectedItem = filteredSalespersonsWithAction.value[highlightedSalespersonIndex.value];
        if (selectedItem) {
            selectSalesperson(selectedItem);
        }
    } else if (salespersonInput.value.trim()) {
        // 如果没有高亮项，但输入框有内容，尝试添加
        const inputTrimmed = salespersonInput.value.trim();
        const exactMatch = salespersons.value.some(p => p === inputTrimmed);
        if (!exactMatch) {
            addNewSalespersonAction(inputTrimmed);
        } else {
            // 是现有售货员，确认选择
             newRecord.value.salesperson = inputTrimmed;
             isSalespersonDropdownOpen.value = false;
        }
    } else {
        // 输入为空，关闭下拉
        isSalespersonDropdownOpen.value = false;
        newRecord.value.salesperson = null;
        salespersonInput.value = '';
    }
}

function highlightNextSalesperson() {
    if (!isSalespersonDropdownOpen.value) return;
    const count = filteredSalespersonsWithAction.value.length;
    if (count === 0) return;
    highlightedSalespersonIndex.value = (highlightedSalespersonIndex.value + 1) % count;
    scrollToHighlighted();
}

function highlightPrevSalesperson() {
    if (!isSalespersonDropdownOpen.value) return;
    const count = filteredSalespersonsWithAction.value.length;
    if (count === 0) return;
    highlightedSalespersonIndex.value = (highlightedSalespersonIndex.value - 1 + count) % count;
    scrollToHighlighted();
}

// 滚动到高亮项 (需要给 ul 添加 ref)
function scrollToHighlighted() {
    const listElement = document.querySelector('.salesperson-dropdown'); // 简单选择器，可用 ref 替代
    if (!listElement) return;
    const highlightedElement = listElement.children[highlightedSalespersonIndex.value];
    if (highlightedElement) {
        highlightedElement.scrollIntoView({ block: 'nearest' });
    }
}

// --- 新增/修改：编辑弹窗方法 ---
function openEditModal(record) {
    // 创建一个记录的副本进行编辑，避免直接修改原始数据
    editingRecord.value = { ...record };
    console.log("Opening edit modal for:", editingRecord.value);
}

function closeEditModal() {
    editingRecord.value = null;
}

async function handleSaveEdit(updatedData) {
    if (!editingRecord.value) return;

    console.log("Saving edit:", updatedData);

    // 记录原始时间和更新后的时间
    const originalTime = editingRecord.value.time;
    const newTime = updatedData.time || originalTime;

    try {
        // 调用 service 更新数据 (updatedData 包含 notes) - 注意这是异步函数
        const result = await updateFinancialRecord(editingRecord.value.id, updatedData);

        if (result.success) {
            // 检查时间是否发生了变化
            const timeChanged = originalTime !== newTime;

            if (timeChanged) {
                showFeedback('记录更新成功！记录已按新时间重新排序。', 'success');
            } else {
                showFeedback('记录更新成功！', 'success');
            }

            closeEditModal();
            // sortedRecords 计算属性会自动重新排序，无需手动刷新
        } else {
            showFeedback(`更新失败: ${result.message}`, 'error');
            // 可选：保持弹窗打开让用户重试
        }
    } catch (error) {
        console.error('更新记录时发生错误:', error);
        showFeedback('更新失败: 发生未知错误', 'error');
    }
}

// --- 结束：编辑弹窗方法 ---

// --- 辅助：添加反馈显示（如果还没有） ---
const feedbackMessage = ref('');
const feedbackType = ref('info'); // 'info', 'success', 'error'
let feedbackTimeout = null;

function showFeedback(message, type = 'info', duration = 3000) {
  feedbackMessage.value = message;
  feedbackType.value = type;
  clearTimeout(feedbackTimeout);
  if (duration > 0) {
    feedbackTimeout = setTimeout(() => {
      feedbackMessage.value = '';
    }, duration);
  }
}

// 格式化保存时间
function formatSaveTime(saveTime) {
  if (!saveTime) return '';
  const now = new Date();
  const diff = now - saveTime;

  if (diff < 1000) {
    return '刚刚';
  } else if (diff < 60000) {
    return `${Math.floor(diff / 1000)}秒前`;
  } else if (diff < 3600000) {
    return `${Math.floor(diff / 60000)}分钟前`;
  } else {
    return saveTime.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    });
  }
}
// --- 结束：反馈显示 ---

// --- 结束：售货员组合框方法 ---

// --- 新增：商品组合框方法 ---
// 处理输入框点击
function handleProductInputClick() {
    if (!isProductSearchMode.value) {
        // 非搜索模式：显示下拉菜单但不激活输入
        isProductDropdownOpen.value = true;
        highlightedProductIndex.value = -1;
        // 确保清除任何待处理的blur超时
        clearTimeout(productBlurTimeout);
    }
    // 搜索模式下正常处理点击（已经可以输入）
}

// 进入搜索模式
function enterSearchMode() {
    isProductSearchMode.value = true;
    productInput.value = ''; // 清空输入框
    highlightedProductIndex.value = -1;
    // 聚焦到输入框并激活键盘
    nextTick(() => {
        if (productInputRef.value) {
            productInputRef.value.focus();
        }
    });
}

// 退出搜索模式
function exitSearchMode() {
    isProductSearchMode.value = false;
    isProductDropdownOpen.value = false;
    highlightedProductIndex.value = -1;
    // 如果没有选中商品，清空输入框
    if (!newRecord.value.productId) {
        productInput.value = '';
    }
}

function handleProductInput() {
    if (!isProductSearchMode.value) return;

    isProductDropdownOpen.value = true;
    highlightedProductIndex.value = -1; // Reset highlight on input
    // 如果当前选中的商品与输入不匹配，清空选择
    if (newRecord.value.productId && productInput.value) {
        const selectedProduct = products.value.find(p => p.id === newRecord.value.productId);
        if (!selectedProduct || selectedProduct.name !== productInput.value) {
            newRecord.value.productId = '';
            newRecord.value.saleAmount = null;
        }
    }
}

function handleProductBlur() {
    // 延迟关闭，以便可以点击下拉项
    productBlurTimeout = setTimeout(() => {
        if (isProductSearchMode.value) {
            // 搜索模式下的blur处理
            isProductDropdownOpen.value = false;
            highlightedProductIndex.value = -1;
            // 如果输入框内容不匹配任何商品，清空选择
            if (productInput.value && !newRecord.value.productId) {
                const exactMatch = products.value.find(p => p.name === productInput.value);
                if (exactMatch) {
                    newRecord.value.productId = exactMatch.id;
                    newRecord.value.saleAmount = exactMatch.price;
                    // 选择商品后退出搜索模式
                    isProductSearchMode.value = false;
                } else {
                    // 清空无效输入但保持搜索模式，让用户可以重新输入
                    productInput.value = '';
                }
            } else if (!productInput.value && !newRecord.value.productId) {
                // 如果没有输入且没有选择商品，退出搜索模式
                isProductSearchMode.value = false;
            }
        } else {
            // 非搜索模式下简单关闭下拉菜单
            isProductDropdownOpen.value = false;
            highlightedProductIndex.value = -1;
        }
    }, 200); // 200ms 延迟
}

function selectProduct(product) {
    clearTimeout(productBlurTimeout); // 取消关闭
    productInput.value = product.name;
    newRecord.value.productId = product.id;
    newRecord.value.saleAmount = product.price; // 自动填充价格
    isProductDropdownOpen.value = false;
    highlightedProductIndex.value = -1;
    // 选择商品后退出搜索模式
    isProductSearchMode.value = false;
}

function handleProductEnter() {
    if (isProductDropdownOpen.value && highlightedProductIndex.value >= 0) {
        // 如果有高亮项，选择它
        if (!isProductSearchMode.value && highlightedProductIndex.value === -1) {
            // 非搜索模式下，-1表示"手动输入搜索"选项
            enterSearchMode();
            return;
        }

        const adjustedIndex = isProductSearchMode.value ? highlightedProductIndex.value : highlightedProductIndex.value - 1;
        const selectedProduct = filteredProducts.value[adjustedIndex];
        if (selectedProduct) {
            selectProduct(selectedProduct);
        }
    } else if (isProductSearchMode.value && productInput.value.trim()) {
        // 搜索模式下，如果没有高亮项但输入框有内容，尝试匹配
        const inputTrimmed = productInput.value.trim();
        const exactMatch = products.value.find(p => p.name === inputTrimmed);
        if (exactMatch) {
            selectProduct(exactMatch);
        } else {
            // 没有匹配的商品，关闭下拉
            isProductDropdownOpen.value = false;
        }
    } else {
        // 输入为空或非搜索模式，关闭下拉
        isProductDropdownOpen.value = false;
        if (isProductSearchMode.value) {
            exitSearchMode();
        }
    }
}

function highlightNextProduct() {
    if (!isProductDropdownOpen.value) return;
    const count = filteredProducts.value.length;
    if (count === 0) return;

    if (isProductSearchMode.value) {
        // 搜索模式：正常导航商品列表
        highlightedProductIndex.value = (highlightedProductIndex.value + 1) % count;
    } else {
        // 非搜索模式：包含"手动输入搜索"选项
        if (highlightedProductIndex.value === -1) {
            highlightedProductIndex.value = 0; // 从搜索选项到第一个商品
        } else {
            highlightedProductIndex.value = (highlightedProductIndex.value + 1) % count;
        }
    }
    scrollToHighlightedProduct();
}

function highlightPrevProduct() {
    if (!isProductDropdownOpen.value) return;
    const count = filteredProducts.value.length;
    if (count === 0) return;

    if (isProductSearchMode.value) {
        // 搜索模式：正常导航商品列表
        highlightedProductIndex.value = highlightedProductIndex.value <= 0 ? count - 1 : highlightedProductIndex.value - 1;
    } else {
        // 非搜索模式：包含"手动输入搜索"选项
        if (highlightedProductIndex.value <= 0) {
            highlightedProductIndex.value = -1; // 回到搜索选项
        } else {
            highlightedProductIndex.value = highlightedProductIndex.value - 1;
        }
    }
    scrollToHighlightedProduct();
}

function scrollToHighlightedProduct() {
    // 滚动到高亮的商品项
    nextTick(() => {
        const dropdown = document.querySelector('.product-dropdown');
        const highlightedItem = dropdown?.children[highlightedProductIndex.value];
        if (highlightedItem) {
            highlightedItem.scrollIntoView({ block: 'nearest' });
        }
    });
}
// --- 结束：商品组合框方法 ---

// 在表单中编辑记录
function editInForm(record) {
    // 设置编辑状态
    isEditing.value = true;
    editingId.value = record.id;

    // 填充表单数据
    newRecord.value = {
        date: record.date,
        time: record.time || new Date().toTimeString().slice(0, 5),
        productId: record.productId,
        salesperson: record.salesperson,
        saleAmount: record.saleAmount,
        notes: record.notes || '',
    };

    // 同步售货员输入框
    salespersonInput.value = record.salesperson || '';

    // 同步商品输入框
    const selectedProduct = products.value.find(p => p.id === record.productId);
    productInput.value = selectedProduct ? selectedProduct.name : '';

    // 滚动到表单顶部
    window.scrollTo({ top: 0, behavior: 'smooth' });

    // 清除错误信息
    addRecordError.value = '';
}

// 取消编辑模式
function cancelEditing() {
    isEditing.value = false;
    editingId.value = null;

    // 重置表单，使用最新的北京时间
    newRecord.value = {
        date: getCurrentDate(),
        time: getCurrentTime(),
        productId: '',
        salesperson: null,
        saleAmount: null,
        notes: '',
    };

    salespersonInput.value = '';
    productInput.value = '';
    isProductSearchMode.value = false;
    addRecordError.value = '';
}

// 添加或更新财务记录
async function handleAddRecord() {
    addRecordError.value = '';

    // 基本验证
    if (!newRecord.value.productId) {
        addRecordError.value = '请选择售出的商品。';
        return;
    }

    // 检查售货员是否已选定
    if (!newRecord.value.salesperson && salespersonInput.value && !salespersons.value.includes(salespersonInput.value)) {
        addRecordError.value = '售货员输入无效或未确认添加。';
        return;
    } else if (!newRecord.value.salesperson && salespersonInput.value) {
        // 如果输入框的值是一个现有的售货员但未赋给 newRecord，则赋值
        newRecord.value.salesperson = salespersonInput.value;
    }

    if (newRecord.value.saleAmount === null || newRecord.value.saleAmount < 0) {
        addRecordError.value = '请输入有效的售出金额。';
        return;
    }

    // 如果是编辑模式
    if (isEditing.value && editingId.value) {
        // 调用更新方法
        const result = await updateFinancialRecord(editingId.value, {
            date: newRecord.value.date,
            time: newRecord.value.time,
            productId: newRecord.value.productId,
            salesperson: newRecord.value.salesperson,
            saleAmount: newRecord.value.saleAmount,
            notes: newRecord.value.notes,
        });

        if (result.success) {
            showFeedback('记录更新成功！', 'success');
            // 退出编辑模式
            isEditing.value = false;
            editingId.value = null;

            // 重置表单，使用最新的北京时间
            newRecord.value = {
                date: getCurrentDate(),
                time: getCurrentTime(),
                productId: '',
                salesperson: null,
                saleAmount: null,
                notes: '',
            };
            salespersonInput.value = '';
            productInput.value = '';
            isProductSearchMode.value = false;
        } else {
            addRecordError.value = result.message || '更新记录失败。';
        }
    } else {
        // 添加新记录
        // 使用表单中的时间或当前时间
        const timeToUse = newRecord.value.time || new Date().toTimeString().slice(0, 5);

        const result = await addFinancialRecord({
            date: newRecord.value.date,
            time: timeToUse,
            productId: newRecord.value.productId,
            salesperson: newRecord.value.salesperson || (salespersonInput.value && salespersons.value.includes(salespersonInput.value) ? salespersonInput.value : null),
            saleAmount: newRecord.value.saleAmount,
            notes: newRecord.value.notes,
        });

        if (result.success) {
            // 清空表单，使用最新的北京时间
            newRecord.value = {
                date: getCurrentDate(),
                time: getCurrentTime(),
                productId: '',
                salesperson: null,
                saleAmount: null,
                notes: '',
            };
            salespersonInput.value = '';
            productInput.value = '';
            isProductSearchMode.value = false;
            addRecordError.value = '';

            // 可以选择性地显示库存更新的消息 result.stockMessage
            if (result.stockMessage && result.stockMessage.includes('卖完了')) {
                alert(result.stockMessage); // 用弹窗提示售罄
            }
        } else {
            // 显示来自 service 的错误消息 (可能是库存不足、商品不存在等)
            addRecordError.value = result.message || '添加记录失败。';
        }
    }
}

// --- 修改：删除记录时恢复库存 ---
async function deleteRecord(recordId) {
    if (confirm('确定要删除这条记录吗？')) {
        try {
            // 调用 service 层的删除方法，它会自动处理库存恢复和数据同步
            const result = await deleteFinancialRecord(recordId);

            if (result.success) {
                showFeedback('销售记录已删除。', 'success');
            } else {
                showFeedback(`删除失败: ${result.message}`, 'error');
            }
        } catch (error) {
            console.error('删除记录时发生错误:', error);
            showFeedback('删除失败: 发生未知错误', 'error');
        }
    }
}
// --- 结束：修改删除记录逻辑 ---

// 监听商品列表变化，如果当前选中的商品被删除了，重置选择
watch(products, (newProducts) => {
    if (newRecord.value.productId && !newProducts.some(p => p.id === newRecord.value.productId)) {
        newRecord.value.productId = '';
        newRecord.value.saleAmount = null;
    }
}, { deep: true });

// 监听返利系数编辑器弹窗的显示状态，当弹窗显示时自动聚焦输入框
watch(showRebateEditor, (newValue) => {
    if (newValue) {
        // 更新输入框的值为当前返利比例
        rebateRatePercent.value = Math.round(rebateRate.value * 100);
        // 下一个 tick 后聚焦输入框
        nextTick(() => {
            const rebateInput = document.querySelector('.rebate-editor-modal input');
            if (rebateInput) {
                rebateInput.focus();
                rebateInput.select(); // 选中所有文本，方便直接输入
            }
        });
    }
});

// 当选中一个售货员后，同步更新输入框的值
watch(() => newRecord.value.salesperson, (newValue) => {
    if (newValue && salespersonInput.value !== newValue) {
        salespersonInput.value = newValue;
    }
     if (newValue === null && salespersonInput.value !== '') {
         // 如果通过其他方式清空了选择（例如，表单重置），也清空输入框
        // salespersonInput.value = ''; // 可选行为
    }
});

// 当选中一个商品后，同步更新输入框的值
watch(() => newRecord.value.productId, (newValue) => {
    if (newValue) {
        const selectedProduct = products.value.find(p => p.id === newValue);
        if (selectedProduct && productInput.value !== selectedProduct.name) {
            productInput.value = selectedProduct.name;
        }
    } else if (productInput.value !== '') {
        // 如果通过其他方式清空了选择（例如，表单重置），也清空输入框
        productInput.value = '';
    }
});

// --- 新增：删除售货员处理 ---
function handleDeleteSalesperson(name) {
    // 阻止事件冒泡，防止触发 mousedown 导致选择
    // event.stopPropagation();
    // @close.stop 已经做了这个

    if (confirm(`确定要删除售货员 "${name}" 吗？\n注意：这不会修改已有的销售记录中的售货员信息。`)) {
        const result = deleteSalesperson(name);
        if (!result.success) {
            alert(result.message);
        }
        // 下拉列表会自动更新，因为 salespersons 是响应式的
        // 可能需要重置输入框如果当前输入的是被删除的售货员
        if (salespersonInput.value === name) {
             salespersonInput.value = '';
             newRecord.value.salesperson = null;
        }
        // 关闭下拉菜单可能不是最佳体验，让用户看到删除了
        // isSalespersonDropdownOpen.value = false;
    }
}
// --- 结束：删除售货员处理 ---

// --- 返利比例设置 ---
// 更新返利比例
async function updateRebateRateSetting() {
    // 将百分比转换为小数
    const rate = parseFloat(rebateRatePercent.value) / 100;

    // 验证输入
    if (isNaN(rate) || rate < 0 || rate > 1) {
        showFeedback('请输入有效的返利比例（0-100）', 'error');
        // 重置为当前值
        rebateRatePercent.value = Math.round(rebateRate.value * 100);
        return;
    }

    // 更新当天的返利比例设置
    const success = await updateRebateRate(rate, today.value);
    if (success) {
        showFeedback(`今日返利比例已更新为 ${rebateRatePercent.value}%`, 'success');

        // 关闭返利系数编辑器弹窗
        showRebateEditor.value = false;

        // 如果当日总结已显示，重新生成
        if (showDailySummary.value) {
            checkoutToday();
        }
    } else {
        showFeedback('更新返利比例失败', 'error');
        // 重置为当前值
        rebateRatePercent.value = Math.round(rebateRate.value * 100);
    }
}

// 初始化
onMounted(() => {
    // 初始化返利比例输入框
    rebateRatePercent.value = Math.round(rebateRate.value * 100);
});
// --- 结束：返利比例设置 ---

// --- 导出功能 --- //

// 导出销售流水和当日结账总结到一张图片
async function exportCombinedImage() {
    // 如果当日结账总结未显示，先触发结账功能
    if (!showDailySummary.value) {
        checkoutToday();
        // 如果结账后仍未显示总结（可能是因为今天没有销售记录），则只导出表格
        if (!showDailySummary.value) {
            await exportTableToImage('finance-table', `销售流水-${today.value}`);
            return;
        }
        // 等待DOM更新
        await nextTick();
    }

    // 创建一个临时容器来包含两个元素
    const container = document.createElement('div');
    container.style.backgroundColor = 'white';
    container.style.padding = '20px';
    container.style.width = 'fit-content';

    // 获取表格和总结元素
    const tableElement = document.getElementById('finance-table');
    const summaryElement = document.querySelector('.daily-summary');

    if (!tableElement || !summaryElement) {
        alert('无法找到要导出的元素。');
        return;
    }

    // 克隆元素到临时容器
    const tableClone = tableElement.cloneNode(true);
    const summaryClone = summaryElement.cloneNode(true);

    // 移除总结中的"关闭总结"按钮
    const closeButton = summaryClone.querySelector('button');
    if (closeButton) {
        closeButton.remove();
    }

    // 移除表格中的编辑返利按钮，但保留返利系数显示
    const editRebateButtons = tableClone.querySelectorAll('.edit-rebate-btn');
    editRebateButtons.forEach(button => {
        button.remove();
    });

    // 添加到临时容器
    container.appendChild(tableClone);
    container.appendChild(document.createElement('br'));
    container.appendChild(summaryClone);

    // 临时添加到文档以便渲染
    document.body.appendChild(container);
    container.style.position = 'absolute';
    container.style.left = '-9999px';

    try {
        // 渲染为图片
        const canvas = await html2canvas(container, {
            useCORS: true,
            backgroundColor: 'white',
            scale: window.devicePixelRatio || 1, // 提高清晰度
        });

        // 导出图片
        const image = canvas.toDataURL('image/png');
        const link = document.createElement('a');
        link.href = image;
        link.download = `销售流水和结账总结-${today.value}.png`;
        link.click();
    } catch (error) {
        console.error('导出图片失败:', error);
        alert('导出图片失败，请查看控制台获取更多信息。');
    } finally {
        // 清理临时元素
        document.body.removeChild(container);
    }
}

// 通用的导出表格到图片的函数 (与 ProductManagement 类似，但针对此页面的表格 ID)
async function exportTableToImage(elementId, filename = 'export') {
    const tableElement = document.getElementById(elementId);
    if (!tableElement) {
        alert('无法找到要导出的表格。');
        return;
    }
    try {
        const canvas = await html2canvas(tableElement, { useCORS: true });
        const image = canvas.toDataURL('image/png');
        const link = document.createElement('a');
        link.href = image;
        link.download = `${filename}.png`;
        link.click();
    } catch (error) {
        console.error('导出图片失败:', error);
        alert('导出图片失败，请查看控制台获取更多信息。');
    }
}

// 导出财务数据到 Excel
function exportFinanceToExcel() {
    if (sortedRecords.value.length === 0) {
        alert('今天暂无销售记录可以导出。');
        return;
    }

    // 准备数据
    const dataToExport = sortedRecords.value.map(record => {
        const rebate = calculateRebate(record);
        const profit = calculateProfit(record);
        return {
            '日期': record.date,
            '时间': record.time || '',
            '卖出物品': getProductName(record.productId),
            '售货员': record.salesperson || '-',
            '售出金额': record.saleAmount,
            '返利': rebate > 0 ? rebate : '', // 空白或 0
            '利润': profit
        };
    });

    // 添加总计行 (可选)
    dataToExport.push({}); // 添加空行分隔
    dataToExport.push({
        '日期': '总计',
        '售出金额': totalSaleAmount.value,
        '利润': totalProfit.value
    });


    const ws = XLSX.utils.json_to_sheet(dataToExport, {
        header: ['日期', '时间', '卖出物品', '售货员', '售出金额', '返利', '利润'] // 明确表头顺序
    });

    // 可以尝试设置列宽 (可选)
    ws['!cols'] = [
        { wch: 12 }, // 日期
        { wch: 8 },  // 时间
        { wch: 25 }, // 卖出物品
        { wch: 15 }, // 售货员
        { wch: 12 }, // 售出金额
        { wch: 10 }, // 返利
        { wch: 12 }  // 利润
    ];

    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, `销售流水-${today.value}`);
    XLSX.writeFile(wb, `销售流水-${today.value}.xlsx`);
}

// --- 今日结账逻辑 ---
function checkoutToday() {
  // 使用计算属性中的今日日期
  const todayRecords = sortedRecords.value; // 已经是过滤好的当天记录

  if (todayRecords.length === 0) {
    feedbackMessage.value = '今天暂无销售记录，无法结账。';
    feedbackType.value = 'warning';
    setTimeout(() => feedbackMessage.value = '', 3000);
    showDailySummary.value = false;
    return;
  }

  let totalRevenue = 0;
  let totalProfitCalc = 0;
  const productSales = {};
  const salespersonSales = {}; // 用于计算销售额和返利 { salespersonName: { totalAmount: 0, totalRebate: 0 } }

  todayRecords.forEach(record => {
    const saleAmount = record.saleAmount || 0;
    const rebate = calculateRebate(record);
    const profit = calculateProfit(record);
    const productName = getProductName(record.productId) || '未知商品';

    totalRevenue += saleAmount;
    totalProfitCalc += profit;

    // 统计商品销售
    if (!productSales[productName]) {
      productSales[productName] = { quantity: 0, amount: 0 };
    }
    productSales[productName].quantity += 1; // 假设每条记录代表一件商品，如果不是，需要调整
    productSales[productName].amount += saleAmount;

    // 统计销售员业绩和返利
    if (record.salesperson) {
      if (!salespersonSales[record.salesperson]) {
        salespersonSales[record.salesperson] = { totalAmount: 0, totalRebate: 0 };
      }
      salespersonSales[record.salesperson].totalAmount += saleAmount;
      salespersonSales[record.salesperson].totalRebate += rebate;
    }
  });

  // 找出销售冠军
  let topSalesperson = '';
  let topSalespersonAmount = 0;
  for (const name in salespersonSales) {
    if (salespersonSales[name].totalAmount > topSalespersonAmount) {
      topSalespersonAmount = salespersonSales[name].totalAmount;
      topSalesperson = name;
    }
  }

  // 提取销售员返利
  const salespersonRebates = {};
  for (const name in salespersonSales) {
    salespersonRebates[name] = salespersonSales[name].totalRebate;
  }

  // 更新总结数据
  dailySummary.value = {
    date: today,
    totalRevenue: totalRevenue,
    totalProfit: totalProfitCalc,
    topSalesperson: topSalesperson,
    topSalespersonAmount: topSalespersonAmount,
    productSales: productSales,
    salespersonRebates: salespersonRebates
  };

  showDailySummary.value = true; // 显示总结
  feedbackMessage.value = ''; // 清除之前的反馈消息
}
// --- 结束：今日结账逻辑 ---

// --- 结束导出功能 --- //

</script>

<style scoped>
.add-finance-form {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #eee;
  border-radius: 4px;
  display: flex;
  flex-wrap: wrap; /* 允许换行 */
  gap: 10px; /* 元素间距 */
  align-items: center; /* 垂直居中对齐 */
}

/* 主要输入行样式 */
.main-input-row {
  display: flex;
  gap: 10px;
  width: 100%;
  align-items: flex-start; /* 顶部对齐，因为下拉菜单可能不同高度 */
}

.main-input-row > * {
  flex: 1; /* 每个元素平均分配宽度 */
  min-width: 0; /* 允许flex项目缩小 */
}

.main-input-row input[type="number"] {
  max-width: 150px; /* 金额输入框不需要太宽 */
}

/* 移动端适配 */
@media (max-width: 768px) {
  .main-input-row {
    flex-direction: column; /* 移动端垂直排列 */
    gap: 8px;
  }

  .main-input-row > * {
    flex: none; /* 移动端不使用flex分配 */
    width: 100%; /* 每个元素占满宽度 */
  }

  .main-input-row input[type="number"] {
    max-width: none; /* 移动端金额输入框也占满宽度 */
  }

  .product-combobox {
    min-width: auto; /* 移动端不设置最小宽度 */
  }

  .salesperson-combobox {
    min-width: auto; /* 移动端不设置最小宽度 */
  }
}

.add-finance-form .form-header {
  width: 100%; /* 标题区域占满整行 */
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.add-finance-form h3 {
  margin: 0;
  text-align: left;
}

.form-actions {
  display: flex;
  gap: 10px;
}

.cancel-edit-btn {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 3px;
  cursor: pointer;
}

.cancel-edit-btn:hover {
  background-color: #5a6268;
}

.add-finance-form input,
.add-finance-form select,
.add-finance-form textarea,
.add-finance-form button {
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.add-finance-form .notes-input {
  width: 100%;
  resize: vertical;
  min-height: 60px;
}

.time-input {
  display: flex;
  flex-direction: column;
  margin-bottom: 10px;
}

.time-input label {
  margin-bottom: 5px;
  font-size: 0.9em;
  color: #555;
}

.time-input input {
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

/* --- 新增：组合框样式 --- */
.salesperson-combobox {
    position: relative; /* 使得下拉列表可以相对定位 */
    min-width: 150px; /* 根据需要调整宽度 */
}

.product-combobox {
    position: relative; /* 使得下拉列表可以相对定位 */
    min-width: 250px; /* 商品组合框需要更宽的空间 */
    width: 100%; /* 占满可用宽度 */
}

.salesperson-combobox input, .product-combobox input {
    width: 100%;
    box-sizing: border-box; /* 防止 padding 导致宽度超出 */
}

.salesperson-dropdown, .product-dropdown {
    position: absolute;
    top: 100%; /* 显示在输入框下方 */
    left: 0;
    right: 0;
    background-color: white;
    border: 1px solid #ccc;
    border-top: none;
    border-radius: 0 0 4px 4px;
    list-style: none;
    padding: 0;
    margin: 0;
    max-height: 200px; /* 限制最大高度，出现滚动条 */
    overflow-y: auto;
    z-index: 10; /* 确保在其他元素之上 */
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    font-family: inherit; /* 继承父元素字体 */
    font-size: 14px; /* 设置基础字体大小 */
}

.salesperson-dropdown li {
    padding: 4px 8px; /* 调整 padding 以适应 Tag */
    cursor: pointer;
    display: flex; /* 让 Tag 内部元素对齐 */
    align-items: center;
}

.product-dropdown li {
    padding: 6px 10px; /* 减少内边距 */
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
    line-height: 1.2; /* 设置行高 */
    min-height: 32px; /* 设置最小高度 */
    display: flex; /* 确保flex布局 */
    align-items: center; /* 垂直居中 */
}

.product-dropdown li:last-child {
    border-bottom: none;
}

.salesperson-dropdown li:not(.add-option):hover, /* 非添加选项才有 hover 效果 */
.salesperson-dropdown li.highlighted:not(.add-option) {
    background-color: #f0f0f0;
}

.product-dropdown li:hover,
.product-dropdown li.highlighted {
    background-color: #f0f8ff;
}

/* Tag 本身有内边距，可能需要调整 li 的 padding */
.salesperson-dropdown li .el-tag {
    margin: 2px 0; /* 给 Tag 一点垂直间距 */
    width: 100%; /* 让 Tag 充满 li */
    justify-content: space-between; /* 让关闭按钮靠右 */
}

/* 商品选项样式 */
.product-option {
    display: flex;
    align-items: center;
    width: 100%;
    gap: 10px; /* 增加间距 */
    white-space: nowrap; /* 防止文字换行 */
}

.product-name {
    font-weight: 500;
    color: #333;
    flex: 1;
    font-size: 14px; /* 设置合适的字体大小 */
    white-space: nowrap; /* 防止文字换行 */
    overflow: hidden; /* 隐藏溢出文字 */
    text-overflow: ellipsis; /* 显示省略号 */
    min-width: 0; /* 允许flex项目缩小 */
    margin-right: auto; /* 推送右侧内容到最右边 */
}

.product-price {
    color: #007bff;
    font-weight: 600;
    font-size: 13px; /* 稍小的字体 */
    white-space: nowrap; /* 防止换行 */
    flex-shrink: 0; /* 价格不缩小 */
    margin-right: 8px; /* 与库存信息的间距 */
}

.product-stock {
    font-size: 11px; /* 更小的字体 */
    color: #666;
    padding: 1px 4px; /* 减少内边距 */
    border-radius: 2px; /* 更小的圆角 */
    background-color: #f8f9fa;
    white-space: nowrap; /* 防止换行 */
    flex-shrink: 0; /* 库存信息不缩小 */
    min-width: 45px; /* 设置最小宽度确保对齐 */
    text-align: center; /* 文字居中 */
}

.product-stock.low-stock {
    color: #856404;
    background-color: #fff3cd;
}

.product-stock.out-of-stock {
    color: #721c24;
    background-color: #f8d7da;
}

/* 添加选项的特殊样式（如果需要） */
.salesperson-dropdown li.add-option {
    color: #606266;
    font-style: italic;
}
.salesperson-dropdown li.add-option.highlighted {
     background-color: #e0e0e0;
}

/* 手动输入搜索选项样式 */
.search-option {
    border-bottom: 1px solid #e0e0e0 !important;
    background-color: #f8f9fa;
}

.search-option:hover,
.search-option.highlighted {
    background-color: #e3f2fd !important;
}

.search-option-content {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 2px 0;
}

.search-icon {
    font-size: 14px;
    color: #666;
}

.search-text {
    color: #666;
    font-size: 13px;
    font-style: italic;
}
/* --- 结束：组合框样式 --- */

.add-finance-form button {
  background-color: #42b983;
  color: white;
  cursor: pointer;
  border: none;
}
.add-finance-form button:hover {
  background-color: #36a374;
}

.error-message {
    color: red;
    font-size: 0.9em;
    width: 100%; /* 错误消息占满整行 */
    margin-top: 5px;
}

.finance-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
}

.finance-table th,
.finance-table td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}

.finance-table th {
  background-color: #f2f2f2;
}

.finance-table tfoot td {
  font-weight: bold;
}

.finance-table .delete-button {
    background-color: #f44336; /* Red */
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 0.8em;
}

.finance-table .delete-button:hover {
    background-color: #da190b;
}

/* Add style for the edit button */
.finance-table .edit-button {
    background-color: #ffc107; /* Yellow */
    color: #333;
    border: none;
    padding: 4px 8px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 0.8em;
    margin-right: 5px; /* Add some space between buttons */
}

.finance-table .edit-button:hover {
    background-color: #e0a800;
}

/* Add style for the edit-in-form button */
.finance-table .edit-in-form-button {
    background-color: #17a2b8; /* Teal */
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 0.8em;
    margin-right: 5px;
}

.finance-table .edit-in-form-button:hover {
    background-color: #138496;
}

/* --- 移除之前的售货员标签样式 --- */
/*
.salesperson-tag {
    ...
}
*/
/* --- 结束移除 --- */

/* 添加导出按钮样式 (可以和 ProductManagement 共享，或者在这里定义) */
.export-buttons {
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
    display: flex;
    gap: 10px;
}

.export-buttons button {
    padding: 8px 15px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.export-buttons button:hover {
    background-color: #0056b3;
}

.export-buttons .history-link {
    padding: 8px 15px;
    background-color: #673ab7;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.export-buttons .history-link:hover {
    background-color: #5e35b1;
}

.export-buttons .checkout-button {
    background-color: #ff9800;
}

.export-buttons .checkout-button:hover {
    background-color: #f57c00;
}

/* 百分比输入组样式 */
.rate-input-group {
    position: relative;
    display: inline-flex;
    align-items: center;
}

.rate-input-group input {
    width: 60px;
    padding: 6px 20px 6px 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 14px;
}

.percent-sign {
    position: absolute;
    right: 8px;
    color: #666;
}

/* --- 新增：反馈消息样式 --- */
.feedback {
  margin-top: 15px;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid transparent;
}
.feedback.info {
  color: #004085;
  background-color: #cce5ff;
  border-color: #b8daff;
}
.feedback.success {
  color: #155724;
  background-color: #d4edda;
  border-color: #c3e6cb;
}
.feedback.error {
  color: #721c24;
  background-color: #f8d7da;
  border-color: #f5c6cb;
}
/* --- 结束：反馈消息样式 --- */

/* 备注列样式 */
.notes-cell {
  max-width: 150px; /* 限制最大宽度 */
  white-space: nowrap; /* 防止换行 */
  overflow: hidden; /* 隐藏溢出部分 */
  text-overflow: ellipsis; /* 显示省略号 */
  cursor: default; /* 鼠标悬停时显示默认光标，配合 title 属性 */
}

/* 返利率样式 */
.rebate-rate {
  font-size: 12px;
  color: #666;
  margin-top: 3px;
}

/* 返利表头样式 */
.rebate-header {
  position: relative;
  display: flex;
  align-items: center;
  gap: 5px;
  white-space: nowrap;
}

.edit-rebate-btn {
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
  font-size: 12px;
  padding: 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  transition: all 0.2s;
}

.edit-rebate-btn:hover {
  background-color: #f0f0f0;
  color: #333;
}

.edit-icon {
  font-size: 10px;
}

/* 返利系数编辑器弹窗样式 */
.rebate-editor-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.rebate-editor-content {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  width: 300px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.rebate-editor-content h3 {
  margin-top: 0;
  margin-bottom: 20px;
  text-align: center;
  color: #333;
}

.rebate-editor-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.modal-input-group {
  width: 100%;
}

.modal-input-group input {
  width: 100%;
  padding: 10px 30px 10px 10px;
  font-size: 16px;
  text-align: center;
}

.rebate-editor-buttons {
  display: flex;
  justify-content: space-between;
  gap: 10px;
  margin-top: 10px;
}

.save-rebate-btn, .cancel-rebate-btn {
  flex: 1;
  padding: 8px 0;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s;
}

.save-rebate-btn {
  background-color: #4caf50;
  color: white;
}

.save-rebate-btn:hover {
  background-color: #45a049;
}

.cancel-rebate-btn {
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
}

.cancel-rebate-btn:hover {
  background-color: #e0e0e0;
}

/* 当日结账总结样式 */
.daily-summary {
  margin-top: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: #f9f9f9;
}

.daily-summary h3 {
  margin-top: 0;
  color: #333;
  text-align: center;
  border-bottom: 1px solid #ddd;
  padding-bottom: 10px;
  margin-bottom: 15px;
}

.summary-content {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.summary-main {
  flex: 1;
  min-width: 250px;
  background-color: white;
  padding: 15px;
  border-radius: 5px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.summary-details {
  flex: 2;
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.summary-section {
  flex: 1;
  min-width: 250px;
  background-color: white;
  padding: 15px;
  border-radius: 5px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.summary-section h4 {
  margin-top: 0;
  color: #555;
  border-bottom: 1px solid #eee;
  padding-bottom: 8px;
}

.summary-section ul {
  padding-left: 20px;
  margin-bottom: 0;
}

.close-summary-btn {
  margin-top: 15px;
  padding: 8px 15px;
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  display: block;
  margin-left: auto;
}

.close-summary-btn:hover {
  background-color: #5a6268;
}

/* 响应式设计 */
@media (min-width: 768px) {
  form.add-finance-form {
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;
    border-radius: 8px;
  }

  .finance-table {
    display: table;
    width: 100%;
  }

  .daily-summary {
    max-width: 90%;
    margin: 30px auto;
  }
}

@media (min-width: 1200px) {
  form.add-finance-form {
    max-width: 800px;
  }

  .daily-summary {
    max-width: 1000px;
  }
}

/* 移动设备样式 */
@media (max-width: 767px) {
  form.add-finance-form {
    padding: 15px;
    width: 100%;
    box-sizing: border-box;
  }

  form.add-finance-form input,
  form.add-finance-form select,
  form.add-finance-form textarea {
    width: 100%;
    box-sizing: border-box;
  }

  .finance-table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }

  .summary-content {
    flex-direction: column;
  }

  .summary-details {
    flex-direction: column;
  }
}

/* 保存状态指示器样式 */
.save-status {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.save-status.saving {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.save-status.success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.save-status.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.save-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.save-time {
  font-size: 12px;
  opacity: 0.8;
  font-weight: normal;
}

/* 保存状态移动端适配 */
@media (max-width: 768px) {
  .save-status {
    top: 10px;
    right: 10px;
    left: 10px;
    text-align: center;
    font-size: 13px;
    padding: 6px 12px;
  }
}

</style>